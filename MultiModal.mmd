%%{init: { "themeVariables": { "fontSize": "18px" } }}%%
flowchart TB
    %% 多维信息感知与融合：大型运输船领域大模型融合多源数据，进行时序理解、威胁预测与态势评估

    %% =============== 输入分区 ===============
    subgraph Inputs[多源感知与情报输入]
        direction LR
        subgraph Units[分散保障单元]
            direction TB
            U1[🚢 小型运输船<br>边缘小模型推理结果]
            U2[🌊 潜艇<br>声呐/磁探 + 小模型结果]
            U3[✈️ 海盗预警机<br>侦察影像 + 目标检测]
            U4[🛩️ 无人机群<br>视频/红外 + 跟踪/重识别]
        end

        subgraph Sensors[船载/外部传感器]
            direction TB
            S1[📡 雷达/ARPA]
            S2[🛰️ AIS/ADS-B]
            S3[🎥 EO/IR 光电]
            S4[🔊 声呐/水文]
            S5[☁️ 气象/海况浮标]
        end

        subgraph Intel[情报与公开信息]
            direction TB
            I1[🛰️ 卫星遥感<br>SAR/光学/多谱]
            I2[🌐 网络情报<br>OSINT/CSINT]
            I3[📰 公开/共享海盗情报<br>ICC-IMB/海事通告]
        end
    end

    %% =============== 处理与特征 ===============
    P1[🧹 数据接入与治理<br>协议适配/格式化/去噪/脱敏]
    P2[🧭 时空对齐<br>时序同步/地理配准/轨迹重采样]
    P3[🧩 特征构建<br>目标/轨迹/事件/上下文]

    %% =============== 模型与推理 ===============
    subgraph Model[船载领域大模型（多模态）]
        direction TB
        M1[🕸️ 知识图谱融合<br>实体对齐/关系抽取]
        M2[📚 上下文增强检索 RAG<br>情报/历史事件/规则]
        M3[🧠 推理与因果分析<br>链式/图推理/反事实]
        M4[🎯 威胁识别与预测<br>意图/能力/机会]
        M5[📈 风险评估与评分<br>概率×影响×时空范围]
        M6[🗺️ 态势生成<br>全面/高精度/多维度]
    end

    %% =============== 输出与联动 ===============
    subgraph Outputs[输出与联动]
        direction LR
        O1[🚨 分级告警与处置建议<br>SOP/预案联动]
        O2[🧭 航线优化与编队调整<br>规避高风险海域]
        O3[🤝 协同任务下发<br>无人机/护航舰/保障单元]
        O4[📊 态势可视化看板<br>时间轴/热力/航迹/区域风险]
    end

    %% =============== 数据资产与治理 ===============
    subgraph Data[数据与监控]
        direction LR
        D1[(🗄️ 数据湖/时序库)]
        D2[(🧪 模型与数据监控<br>延迟/漂移/置信度)]
        D3[(🛡️ 审计与合规<br>日志/可追溯/权限)]
    end

    %% =============== 主干数据流 ===============
    U1 -->|小模型结果| P1
    U2 --> P1
    U3 --> P1
    U4 --> P1
    S1 --> P1
    S2 --> P1
    S3 --> P1
    S4 --> P1
    S5 --> P1
    I1 --> P1
    I2 --> P1
    I3 --> P1

    P1 -->|清洗规范化| P2 -->|时空统一| P3
    P3 --> M1 --> M2 -.-> M3
    P3 --> M3
    M3 --> M4 --> M5 --> M6

    M6 --> O1
    M6 --> O2
    M6 --> O3
    M6 --> O4

    %% =============== 反馈与闭环 ===============
    O1 & O2 & O3 --> F1[🔁 反馈/人机协同<br>专家标注/结果纠偏]
    F1 --> P1

    %% =============== 数据与监控连线 ===============
    P1 -. 存证/原始数据 .-> D1
    P2 -. 中间数据/对齐参数 .-> D1
    P3 -. 特征与索引 .-> D1
    M4 -. 置信度/基线/漂移 .-> D2
    M5 -. 评估指标/校准 .-> D2
    O1 -. 审计轨迹 .-> D3
    O3 -. 授权与合规 .-> D3

    %% =============== 样式 ===============
    %% 子图背景：淡色填充 + 去边框
    style Inputs fill:#FFF7FA,stroke-width:0px,stroke:transparent
    style Units fill:#FFF7FA,stroke-width:0px,stroke:transparent
    style Sensors fill:#F4FFFB,stroke-width:0px,stroke:transparent
    style Intel fill:#F9F7FF,stroke-width:0px,stroke:transparent
    style Model fill:#F5F9FF,stroke-width:0px,stroke:transparent
    style Outputs fill:#FAFFF2,stroke-width:0px,stroke:transparent
    style Data fill:#F7F8FF,stroke-width:0px,stroke:transparent

    %% 节点配色（多巴胺）+ 圆角
    classDef units fill:#FFE3ED,stroke:#FF6B9A,color:#8C1D40,rx:12,ry:12,stroke-width:1px;
    classDef sensors fill:#DFF9F3,stroke:#2DD4BF,color:#0D9488,rx:12,ry:12,stroke-width:1px;
    classDef intel fill:#EEE9FF,stroke:#A78BFA,color:#6D28D9,rx:12,ry:12,stroke-width:1px;
    classDef processNode fill:#FFF2DB,stroke:#FFAF36,color:#7C2D12,rx:12,ry:12,stroke-width:1px;
    classDef modelNode fill:#E8F2FF,stroke:#60A5FA,color:#1E3A8A,rx:12,ry:12,stroke-width:1px;
    classDef outputsNode fill:#F0FCE1,stroke:#A3E635,color:#3F6212,rx:12,ry:12,stroke-width:1px;
    classDef dataNode fill:#EEF0FF,stroke:#6366F1,color:#3730A3,rx:12,ry:12,stroke-width:1px;

    class U1,U2,U3,U4 units
    class S1,S2,S3,S4,S5 sensors
    class I1,I2,I3 intel
    class P1,P2,P3,F1 processNode
    class M1,M2,M3,M4,M5,M6 modelNode
    class O1,O2,O3,O4 outputsNode
    class D1,D2,D3 dataNode

    %% 连线统一样式 + 颜色随起点
    linkStyle default stroke-width:1.5px,opacity:0.9
    %% 0..11: 输入到P1（Units/Sensors/Intel）
    linkStyle 0 stroke:#FF6B9A,color:#FF6B9A
    linkStyle 1 stroke:#FF6B9A,color:#FF6B9A
    linkStyle 2 stroke:#FF6B9A,color:#FF6B9A
    linkStyle 3 stroke:#FF6B9A,color:#FF6B9A
    linkStyle 4 stroke:#2DD4BF,color:#2DD4BF
    linkStyle 5 stroke:#2DD4BF,color:#2DD4BF
    linkStyle 6 stroke:#2DD4BF,color:#2DD4BF
    linkStyle 7 stroke:#2DD4BF,color:#2DD4BF
    linkStyle 8 stroke:#2DD4BF,color:#2DD4BF
    linkStyle 9 stroke:#A78BFA,color:#A78BFA
    linkStyle 10 stroke:#A78BFA,color:#A78BFA
    linkStyle 11 stroke:#A78BFA,color:#A78BFA
    %% 12..13: P1->P2->P3（Process）
    linkStyle 12 stroke:#FFAF36,color:#FFAF36
    linkStyle 13 stroke:#FFAF36,color:#FFAF36
    %% 14..17: P3/M1/M2 -> ...
    linkStyle 14 stroke:#FFAF36,color:#FFAF36
    linkStyle 15 stroke:#60A5FA,color:#60A5FA
    linkStyle 16 stroke:#60A5FA,color:#60A5FA
    linkStyle 17 stroke:#FFAF36,color:#FFAF36
    %% 18..20: 模型主干
    linkStyle 18 stroke:#60A5FA,color:#60A5FA
    linkStyle 19 stroke:#60A5FA,color:#60A5FA
    linkStyle 20 stroke:#60A5FA,color:#60A5FA
    %% 21..24: 模型到输出
    linkStyle 21 stroke:#60A5FA,color:#60A5FA
    linkStyle 22 stroke:#60A5FA,color:#60A5FA
    linkStyle 23 stroke:#60A5FA,color:#60A5FA
    linkStyle 24 stroke:#60A5FA,color:#60A5FA
    %% 25..27: 输出到反馈
    linkStyle 25 stroke:#A3E635,color:#A3E635
    linkStyle 26 stroke:#A3E635,color:#A3E635
    linkStyle 27 stroke:#A3E635,color:#A3E635
    %% 28: 反馈回P1（Process）
    linkStyle 28 stroke:#FFAF36,color:#FFAF36
    %% 29..31: Process 到数据湖
    linkStyle 29 stroke:#FFAF36,color:#FFAF36
    linkStyle 30 stroke:#FFAF36,color:#FFAF36
    linkStyle 31 stroke:#FFAF36,color:#FFAF36
    %% 32..33: 模型到监控
    linkStyle 32 stroke:#60A5FA,color:#60A5FA
    linkStyle 33 stroke:#60A5FA,color:#60A5FA
    %% 34..35: 输出到合规
    linkStyle 34 stroke:#A3E635,color:#A3E635
    linkStyle 35 stroke:#A3E635,color:#A3E635