%% 任务决策与分配（大模型决策 + 小模型执行与反馈闭环）
%% 说明：大模型接收全局态势信息，生成多种可选防御方案/策略/任务路线图；
%%      小模型将宏观决策转化为平台可执行指令，并在执行层收集反馈与局部调整，确保方案落地。

flowchart LR
	%% 输入与态势融合
	GI[🛰️ 全局态势信息\n(情报/雷达/卫星/船舶AIS/气象海况)]:::data --> SF[🧠 多源信息融合/态势感知]:::data

	%% 大模型：威胁分析与方案生成
	subgraph LLM[大模型：决策与任务分配]
		direction TB
		A1[🧠 海盗威胁态势分析\n(热点/可疑轨迹/风险区/时空演化)]:::llm
		SF --> A1
		A2[📝 生成备选防御方案/策略/任务路线图]:::llm
		A1 --> A2
		OP[🗂️ 备选方案集合]:::llm
		A2 --> OP
		S1((🅰️ 方案A)):::option
		S2((🅱️ 方案B)):::option
		S3((🅾️ 方案C)):::option
		OP --> S1
		OP --> S2
		OP --> S3
		E1[⚖️ 多目标评估与排序\n(安全/时效/成本/合规/资源约束)]:::llm
		S1 --> E1
		S2 --> E1
		S3 --> E1
		PSTAR[⭐ 选定方案 P*]:::llm
		E1 --> PSTAR
	end

	%% 人机协同把关（可选）
	H[🤝✅ 人机协同审核与授权]:::human
	PSTAR --> H

	%% 小模型：任务分解与平台级指令生成
	subgraph SLM[小模型：指令生成与执行反馈]
		direction TB
		T1[🧩 任务分解与平台匹配\n(巡逻/护航/预警/通信/规避航线)]:::smlm
		H --> T1
		T2[🛠️ 平台级指令生成\n(接口适配/参数约束/时序编排/容错)]:::smlm
		T1 --> T2
	end

	%% 执行平台
	subgraph PLT[执行平台]
		direction LR
		UAV[🚁 无人机 (UAV)]:::exec
		USV[🚤 无人艇 (USV)]:::exec
		ESC[🛟 护航船只]:::exec
		CSHIP[🛳️ 商船/运输船]:::exec
	end

	T2 --> UAV
	T2 --> USV
	T2 --> ESC
	T2 --> CSHIP

	%% 反馈与闭环
	subgraph FB[反馈与闭环]
		direction TB
		F1[📡 执行反馈采集\n(遥测/状态/告警/目标态势更新)]:::data
		F2[🔄 局部调整与再下发\n(小模型闭环/滚动修正)]:::smlm
		F3[🚨 重大偏差或突发上报\n(触发大模型再规划)]:::llm
	end

	UAV --> F1
	USV --> F1
	ESC --> F1
	CSHIP --> F1

	F1 --> F2 --> T2
	F1 --> F3 --> A1

	%% 样式定义（多巴胺配色 + 子图配色 + 圆角）
	%% 子图配色与去边框
	style LLM fill:#FFF0F6,stroke-width:0px,stroke:transparent,color:#C2185B
	style SLM fill:#E6FFFB,stroke-width:0px,stroke:transparent,color:#00695C
	style PLT fill:#F8FFE5,stroke-width:0px,stroke:transparent,color:#33691E
	style FB  fill:#F5E8FF,stroke-width:0px,stroke:transparent,color:#4A148C

	%% 组件配色（浅色填充）与圆角
	classDef llm fill:#FFE4EE,stroke:#F50057,stroke-width:1.2px,color:#880E4F,rx:6,ry:6;
	classDef smlm fill:#E0F7F4,stroke:#00BFA5,stroke-width:1.2px,color:#004D40,rx:6,ry:6;
	classDef exec fill:#F7FFE0,stroke:#AEEA00,stroke-width:1.2px,color:#33691E,rx:6,ry:6;
	classDef data fill:#E0F7FA,stroke:#00ACC1,stroke-width:1.2px,color:#006064,rx:6,ry:6;
	classDef option fill:#E0F7FF,stroke:#00E5FF,stroke-width:1.2px,stroke-dasharray:4 2,color:#006064,rx:6,ry:6;
	classDef human fill:#FFF3E0,stroke:#FB8C00,stroke-width:1.2px,color:#E65100,rx:6,ry:6;

	%% 连线按起始组件着色（索引从0开始）
	%% 0: GI->SF (data)
	linkStyle 0 stroke:#00ACC1,stroke-width:2px,opacity:0.95
	%% 1: SF->A1 (data)
	linkStyle 1 stroke:#00ACC1,stroke-width:2px,opacity:0.95
	%% 2: A1->A2 (llm)
	linkStyle 2 stroke:#F50057,stroke-width:2px,opacity:0.95
	%% 3: A2->OP (llm)
	linkStyle 3 stroke:#F50057,stroke-width:2px,opacity:0.95
	%% 4-6: OP->S1/S2/S3 (llm)
	linkStyle 4 stroke:#F50057,stroke-width:2px,opacity:0.95
	linkStyle 5 stroke:#F50057,stroke-width:2px,opacity:0.95
	linkStyle 6 stroke:#F50057,stroke-width:2px,opacity:0.95
	%% 7-9: S1/S2/S3 -> E1 (option)
	linkStyle 7 stroke:#00E5FF,stroke-width:2px,opacity:0.95
	linkStyle 8 stroke:#00E5FF,stroke-width:2px,opacity:0.95
	linkStyle 9 stroke:#00E5FF,stroke-width:2px,opacity:0.95
	%% 10: E1->PSTAR (llm)
	linkStyle 10 stroke:#F50057,stroke-width:2px,opacity:0.95
	%% 11: PSTAR->H (llm)
	linkStyle 11 stroke:#F50057,stroke-width:2px,opacity:0.95
	%% 12: H->T1 (human)
	linkStyle 12 stroke:#FB8C00,stroke-width:2px,opacity:0.95
	%% 13: T1->T2 (smlm)
	linkStyle 13 stroke:#00BFA5,stroke-width:2px,opacity:0.95
	%% 14-17: T2->UAV/USV/ESC/CSHIP (smlm)
	linkStyle 14 stroke:#00BFA5,stroke-width:2px,opacity:0.95
	linkStyle 15 stroke:#00BFA5,stroke-width:2px,opacity:0.95
	linkStyle 16 stroke:#00BFA5,stroke-width:2px,opacity:0.95
	linkStyle 17 stroke:#00BFA5,stroke-width:2px,opacity:0.95
	%% 18-21: UAV/USV/ESC/CSHIP -> F1 (exec)
	linkStyle 18 stroke:#AEEA00,stroke-width:2px,opacity:0.95
	linkStyle 19 stroke:#AEEA00,stroke-width:2px,opacity:0.95
	linkStyle 20 stroke:#AEEA00,stroke-width:2px,opacity:0.95
	linkStyle 21 stroke:#AEEA00,stroke-width:2px,opacity:0.95
	%% 22: F1->F2 (data)
	linkStyle 22 stroke:#00ACC1,stroke-width:2px,opacity:0.95
	%% 23: F2->T2 (smlm)
	linkStyle 23 stroke:#00BFA5,stroke-width:2px,opacity:0.95
	%% 24: F1->F3 (data)
	linkStyle 24 stroke:#00ACC1,stroke-width:2px,opacity:0.95
	%% 25: F3->A1 (llm)
	linkStyle 25 stroke:#F50057,stroke-width:2px,opacity:0.95

